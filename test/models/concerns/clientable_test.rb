# frozen_string_literal: true

require 'test_helper'

class ClientableTest < ActiveSupport::TestCase
  setup do
    # Create two tenants
    @tenant1 = Tenant.create!(name: 'Tenant1', domain: 'tenant1.example.com')
    @tenant2 = Tenant.create!(name: 'Tenant2', domain: 'tenant2.example.com')

    # Create people for each tenant
    @person1 = Person.new(first_name: '<PERSON>', last_name: '<PERSON><PERSON>')
    @client1 = Client.new(tax_code: 'PERSON1', tenant: @tenant1, clientable: @person1)
    @client1.save!

    @person2 = Person.new(first_name: '<PERSON>', last_name: '<PERSON>')
    @client2 = Client.new(tax_code: 'PERSON2', tenant: @tenant2, clientable: @person2)
    @client2.save!

    # Create companies for each tenant
    @company1 = Company.new(company_name: 'Company1', registration_number: 'REG1')
    @client3 = Client.new(tax_code: 'COMPANY1', tenant: @tenant1, clientable: @company1)
    @client3.save!

    @company2 = Company.new(company_name: 'Company2', registration_number: 'REG2')
    @client4 = Client.new(tax_code: 'COMPANY2', tenant: @tenant2, clientable: @company2)
    @client4.save!
  end

  test 'tenant isolation for Person model' do
    # Set current tenant to Tenant1
    Current.tenant = @tenant1

    # Should only see people from Tenant1
    assert_equal 1, Person.count
    assert_equal @person1.id, Person.first.id
    assert_nil Person.find_by(id: @person2.id)

    # Set current tenant to Tenant2
    Current.tenant = @tenant2

    # Should only see people from Tenant2
    assert_equal 1, Person.count
    assert_equal @person2.id, Person.first.id
    assert_nil Person.find_by(id: @person1.id)
  end

  test 'tenant isolation for Company model' do
    # Set current tenant to Tenant1
    Current.tenant = @tenant1

    # Should only see companies from Tenant1
    assert_equal 1, Company.count
    assert_equal @company1.id, Company.first.id
    assert_nil Company.find_by(id: @company2.id)

    # Set current tenant to Tenant2
    Current.tenant = @tenant2

    # Should only see companies from Tenant2
    assert_equal 1, Company.count
    assert_equal @company2.id, Company.first.id
    assert_nil Company.find_by(id: @company1.id)
  end

  test 'unscoped queries allows access to all records' do
    # Set current tenant to Tenant1
    Current.tenant = @tenant1

    # Unscoped should allow access to all people
    assert_equal 2, Person.unscoped.count
    assert_not_nil Person.unscoped.find_by(id: @person2.id)

    # Unscoped should allow access to all companies
    assert_equal 2, Company.unscoped.count
    assert_not_nil Company.unscoped.find_by(id: @company2.id)
  end

  test 'tenant isolation when Current.tenant is nil' do
    # When Current.tenant is nil, should see no record
    Current.tenant = nil

    assert_equal 0, Person.count
    assert_equal 0, Company.count
  end

  teardown do
    # Clean up
    Current.tenant = nil
  end
end
