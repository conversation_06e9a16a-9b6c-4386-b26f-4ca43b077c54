# frozen_string_literal: true

module SmartEnergy
  module Api
    module V1
      module Entities
        class ClientEntity < Grape::Entity
          expose :id
          expose :tax_code
          expose :phone_number
          expose :email
          expose :clientable_type, as: :client_type

          expose :clientable, using: SmartEnergy::Api::V1::Entities::PersonEntity, if: lambda { |client|
            client.clientable_type == 'Person'
          }

          expose :clientable, using: SmartEnergy::Api::V1::Entities::CompanyEntity, if: lambda { |client|
            client.clientable_type == 'Company'
          }
        end
      end
    end
  end
end
